import Foundation

// MARK: - Validation UseCase Implementation
class ValidationUseCaseImpl: ValidationUseCase {
    private let validationService: ValidationServiceProtocol
    
    init(validationService: ValidationServiceProtocol) {
        self.validationService = validationService
    }
    
    func validateEmail(_ email: String) -> ValidationResult {
        return validationService.validateEmail(email)
    }
    
    func validatePassword(_ password: String) -> ValidationResult {
        return validationService.validatePassword(password)
    }
    
    func validateCredentials(email: String, password: String) -> (email: ValidationResult, password: ValidationResult) {
        return (
            email: validateEmail(email),
            password: validatePassword(password)
        )
    }
}
