import SwiftUI

// MARK: - Custom Input Field
struct CustomInputField: View {
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    let leadingIcon: String?
    let trailingIcon: String?
    let onTrailingIconTap: (() -> Void)?
    
    var submitLabel: SubmitLabel = .done
    var onSubmit: (() -> Void)? = nil
    var focus: FocusState<Bool>.Binding? = nil
    
    @Environment(\.appColors) var colors
    @FocusState private var internalIsFocused: Bool
    
    private var isFocusedNow: Bool { focus?.wrappedValue ?? internalIsFocused }
    private var effectiveFocus: FocusState<Bool>.Binding { focus ?? $internalIsFocused }
    
    init(
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false,
        leadingIcon: String? = nil,
        trailingIcon: String? = nil,
        submitLabel: SubmitLabel = .done,
        focus: FocusState<Bool>.Binding? = nil,
        onSubmit: (() -> Void)? = nil,
        onTrailingIconTap: (() -> Void)? = nil,
    ) {
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isSecure = isSecure
        self.leadingIcon = leadingIcon
        self.trailingIcon = trailingIcon
        self.onTrailingIconTap = onTrailingIconTap
        self.submitLabel = submitLabel
        self.onSubmit = onSubmit
        self.focus = focus
    }
    
    var body: some View {
        HStack(spacing: 12.h) {
            // Leading Icon
            if let leadingIcon = leadingIcon {
                Image(systemName: leadingIcon)
                    .font(.system(size: 16))
                    .foregroundColor(colors.secondaryText)
                    .frame(width: 16.h, height: 16.h)
            }
            
            // Text Field
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                        .keyboardType(keyboardType)
                        .focused(effectiveFocus)
                        .textInputAutocapitalization(.never)
                        .textFieldStyle(PlainTextFieldStyle())
                        .submitLabel(submitLabel)
                        .onSubmit { onSubmit?() }
                        .body14Regular()
                        .themedTextColor(ThemedTextColor.TextColorType.primary)
                } else {
                    TextField(placeholder, text: $text)
                        .keyboardType(keyboardType)
                        .focused(effectiveFocus)
                        .textInputAutocapitalization(.never)
                        .textFieldStyle(PlainTextFieldStyle())
                        .submitLabel(submitLabel)
                        .onSubmit { onSubmit?() }
                        .body14Regular()
                        .themedTextColor(ThemedTextColor.TextColorType.primary)
                }
            }
            
            // Trailing Icon
            if let trailingIcon = trailingIcon {
                Button(action: {
                    onTrailingIconTap?()
                }) {
                    Image(systemName: trailingIcon)
                        .font(.system(size: 16))
                        .foregroundColor(colors.secondaryText)
                        .frame(width: 16.h, height: 16.h)
                }
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 12.h)
        .background(colors.surface)
        .overlay(
            RoundedRectangle(cornerRadius: 8.h)
                .stroke(colors.borderDefault, lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 8.h))
    }
    
   
}

// MARK: - Preview
struct CustomInputField_Previews: PreviewProvider {
    @State static var text = ""
    @State static var password = ""
    
    static var previews: some View {
        VStack(spacing: 20) {
            CustomInputField(
                text: $text,
                placeholder: "Enter your email",
                keyboardType: .emailAddress,
                leadingIcon: "envelope"
            )
            
            CustomInputField(
                text: $password,
                placeholder: "Enter password",
                isSecure: true,
                
                onTrailingIconTap: { }
            )
        }
        .padding()
        .attachAllEnvironmentObjects()
    }
}
